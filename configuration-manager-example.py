"""
Example implementation of configuration management for the refactored workflow system.
This shows how Python code would read configuration from enhanced JSON files.
"""

import os
import json
import re
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ApiConfiguration:
    """API configuration data class"""
    base_url: str
    timeout_seconds: int
    ssl_verify: bool
    max_retries: int
    retry_delay_seconds: int
    backoff_strategy: str
    endpoints: Dict[str, str]


@dataclass
class CacheConfiguration:
    """Cache configuration data class"""
    enabled: bool
    duration_minutes: int
    key_template: str


class WorkflowConfigurationManager:
    """
    Manages workflow configuration from enhanced JSON files.
    Handles environment-specific settings and variable substitution.
    """
    
    def __init__(self, environment: Optional[str] = None):
        self.environment = environment or self._detect_environment()
        self._config_cache = {}
    
    def _detect_environment(self) -> str:
        """Detect current environment from environment variables"""
        return os.getenv('DJANGO_ENV', os.getenv('ENVIRONMENT', 'development')).lower()
    
    def _substitute_variables(self, value: Any, context: Dict[str, Any] = None) -> Any:
        """
        Substitute environment variables and context variables in configuration values.
        Supports format: ${VAR_NAME:default_value}
        """
        if not isinstance(value, str):
            return value
        
        # Pattern for ${VAR_NAME:default_value} or ${VAR_NAME}
        pattern = r'\$\{([^:}]+)(?::([^}]*))?\}'
        
        def replace_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ''
            
            # Try environment variable first
            env_value = os.getenv(var_name)
            if env_value is not None:
                return env_value
            
            # Try context variable
            if context and var_name in context:
                return str(context[var_name])
            
            # Use default value
            return default_value
        
        return re.sub(pattern, replace_var, value)
    
    def _apply_environment_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment-specific configuration overrides"""
        base_config = config.copy()
        
        # Get environment-specific overrides
        env_config = config.get('configuration', {}).get('environments', {}).get(self.environment, {})
        
        if env_config:
            # Deep merge environment configuration
            configuration = base_config.get('configuration', {})
            
            # Merge API configuration
            if 'api' in env_config:
                api_config = configuration.get('api', {})
                api_config.update(env_config['api'])
                configuration['api'] = api_config
            
            # Merge cache configuration
            if 'cache' in env_config:
                cache_config = configuration.get('execution', {}).get('cache', {})
                cache_config.update(env_config['cache'])
                if 'execution' not in configuration:
                    configuration['execution'] = {}
                configuration['execution']['cache'] = cache_config
            
            # Add credentials to steps if provided
            if 'credentials' in env_config:
                configuration['credentials'] = env_config['credentials']
            
            base_config['configuration'] = configuration
        
        return base_config
    
    def _substitute_config_variables(self, obj: Any, config: Dict[str, Any]) -> Any:
        """Recursively substitute configuration variables in the format {{config.path.to.value}}"""
        if isinstance(obj, dict):
            return {k: self._substitute_config_variables(v, config) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_config_variables(item, config) for item in obj]
        elif isinstance(obj, str):
            # Handle {{config.path.to.value}} substitution
            pattern = r'\{\{config\.([^}]+)\}\}'
            
            def replace_config_var(match):
                path = match.group(1)
                value = self._get_nested_value(config.get('configuration', {}), path)
                return str(value) if value is not None else match.group(0)
            
            return re.sub(pattern, replace_config_var, obj)
        else:
            return obj
    
    def _get_nested_value(self, obj: Dict[str, Any], path: str) -> Any:
        """Get nested value from dictionary using dot notation"""
        keys = path.split('.')
        current = obj
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def load_workflow_config(self, workflow_id: str) -> Dict[str, Any]:
        """Load and process workflow configuration"""
        if workflow_id in self._config_cache:
            return self._config_cache[workflow_id]
        
        # Load base configuration (this would integrate with existing registry system)
        config = self._load_raw_config(workflow_id)
        
        # Apply environment overrides
        config = self._apply_environment_overrides(config)
        
        # Substitute environment variables
        config = self._substitute_variables_recursive(config)
        
        # Substitute configuration references
        config = self._substitute_config_variables(config, config)
        
        # Cache the processed configuration
        self._config_cache[workflow_id] = config
        
        return config
    
    def _load_raw_config(self, workflow_id: str) -> Dict[str, Any]:
        """Load raw configuration from file (placeholder for registry integration)"""
        # This would integrate with the existing workflow registry system
        # For now, simulate loading from file
        config_files = {
            'bvtpa_policy_list': 'enhanced-workflow-config-example.json',
            'bvtpa_policy_details': 'bvtpa-policy-details-workflow.json'
        }
        
        if workflow_id in config_files:
            try:
                with open(config_files[workflow_id], 'r') as f:
                    return json.load(f)
            except FileNotFoundError:
                pass
        
        raise ValueError(f"Workflow configuration not found: {workflow_id}")
    
    def _substitute_variables_recursive(self, obj: Any) -> Any:
        """Recursively substitute environment variables in configuration"""
        if isinstance(obj, dict):
            return {k: self._substitute_variables_recursive(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_variables_recursive(item) for item in obj]
        elif isinstance(obj, str):
            return self._substitute_variables(obj)
        else:
            return obj
    
    def get_api_config(self, workflow_config: Dict[str, Any]) -> ApiConfiguration:
        """Extract API configuration from workflow config"""
        api_config = workflow_config.get('configuration', {}).get('api', {})
        
        return ApiConfiguration(
            base_url=api_config.get('base_url', 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2'),
            timeout_seconds=api_config.get('timeout_seconds', 10),
            ssl_verify=api_config.get('ssl_verify', False),
            max_retries=api_config.get('max_retries', 3),
            retry_delay_seconds=api_config.get('retry_delay_seconds', 3),
            backoff_strategy=api_config.get('backoff_strategy', 'exponential'),
            endpoints=api_config.get('endpoints', {})
        )
    
    def get_cache_config(self, workflow_config: Dict[str, Any]) -> CacheConfiguration:
        """Extract cache configuration from workflow config"""
        cache_config = workflow_config.get('configuration', {}).get('execution', {}).get('cache', {})
        
        return CacheConfiguration(
            enabled=cache_config.get('enabled', True),
            duration_minutes=cache_config.get('duration_minutes', 60),
            key_template=cache_config.get('key_template', 'default_{workflow_id}')
        )
    
    def get_response_fields(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract response field mappings from workflow config"""
        return workflow_config.get('configuration', {}).get('response_fields', {})
    
    def get_error_messages(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract error message templates from workflow config"""
        return workflow_config.get('configuration', {}).get('error_messages', {})
    
    def get_validation_config(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract validation configuration from workflow config"""
        return workflow_config.get('configuration', {}).get('validation', {})


class EnhancedTPAApiService:
    """
    Enhanced TPA API Service that reads configuration from JSON workflow files.
    This replaces the hardcoded constants with configuration-driven approach.
    """
    
    def __init__(self, workflow_id: str):
        self.config_manager = WorkflowConfigurationManager()
        self.workflow_config = self.config_manager.load_workflow_config(workflow_id)
        self.api_config = self.config_manager.get_api_config(self.workflow_config)
        
        # Initialize session with configuration
        import requests
        self.session = requests.Session()
        self.session.verify = self.api_config.ssl_verify
        
        # Set up logging
        import logging
        self.logger = logging.getLogger('django.customer_policy_crm')
        
        self.logger.info(f"EnhancedTPAApiService initialized for workflow {workflow_id}")
        self.logger.debug(f"API Config: base_url={self.api_config.base_url}, timeout={self.api_config.timeout_seconds}s")
    
    def get_endpoint_url(self, endpoint_key: str) -> str:
        """Get full URL for an endpoint using configuration"""
        endpoint_path = self.api_config.endpoints.get(endpoint_key, f"/api/{endpoint_key}")
        return f"{self.api_config.base_url}{endpoint_path}"
    
    def make_request_with_config(self, endpoint_key: str, method: str, **kwargs):
        """Make HTTP request using configuration-driven settings"""
        url = self.get_endpoint_url(endpoint_key)
        
        # Apply configuration defaults
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.api_config.timeout_seconds
        
        # Use configured retry logic
        for attempt in range(self.api_config.max_retries):
            try:
                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()
                return response
            except Exception as e:
                if attempt == self.api_config.max_retries - 1:
                    raise
                
                import time
                delay = self.api_config.retry_delay_seconds
                if self.api_config.backoff_strategy == 'exponential':
                    delay *= (2 ** attempt)
                
                self.logger.warning(f"Request failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                time.sleep(delay)


class EnhancedWorkflowExecutor:
    """
    Enhanced workflow executor that uses configuration-driven field mappings and validation.
    """
    
    def __init__(self, workflow_id: str):
        self.config_manager = WorkflowConfigurationManager()
        self.workflow_config = self.config_manager.load_workflow_config(workflow_id)
        self.response_fields = self.config_manager.get_response_fields(self.workflow_config)
        self.error_messages = self.config_manager.get_error_messages(self.workflow_config)
        self.validation_config = self.config_manager.get_validation_config(self.workflow_config)
    
    def get_field_value(self, response: Dict[str, Any], field_key: str) -> Any:
        """Get field value using configuration-driven field mapping"""
        field_name = self.response_fields.get(field_key, field_key)
        return response.get(field_name)
    
    def validate_citizen_id_response(self, response: Dict[str, Any]) -> bool:
        """Validate citizen ID response using configuration"""
        search_results = self.get_field_value(response, 'citizen_search_results')
        if not search_results or len(search_results) == 0:
            return False
        
        status = search_results[0].get(self.response_fields.get('status_field', 'Status'))
        expected_status = self.validation_config.get('citizen_id_status_success', '1')
        
        return status == expected_status
    
    def get_error_message(self, message_key: str, **kwargs) -> str:
        """Get error message using configuration with template substitution"""
        template = self.error_messages.get(message_key, f"Error: {message_key}")
        return template.format(**kwargs)


# Example usage
if __name__ == "__main__":
    # Initialize configuration manager
    config_manager = WorkflowConfigurationManager(environment='development')
    
    # Load workflow configuration
    workflow_config = config_manager.load_workflow_config('bvtpa_policy_list')
    
    # Get specific configuration sections
    api_config = config_manager.get_api_config(workflow_config)
    cache_config = config_manager.get_cache_config(workflow_config)
    response_fields = config_manager.get_response_fields(workflow_config)
    
    print(f"API Base URL: {api_config.base_url}")
    print(f"Cache Duration: {cache_config.duration_minutes} minutes")
    print(f"Response Fields: {response_fields}")
    
    # Initialize enhanced services
    api_service = EnhancedTPAApiService('bvtpa_policy_list')
    workflow_executor = EnhancedWorkflowExecutor('bvtpa_policy_list')
    
    print("Configuration-driven services initialized successfully!")
