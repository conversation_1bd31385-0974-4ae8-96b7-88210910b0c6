"""
Enhanced workflow configuration management for the refactored policy workflow system.
This module provides configuration-driven approach to replace hardcoded constants.
"""

import os
import json
import re
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .policy_workflow_registry import get_workflow_registry

logger = logging.getLogger('django.customer_policy_crm')


@dataclass
class ApiConfiguration:
    """API configuration data class"""
    base_url: str
    timeout_seconds: int
    ssl_verify: bool
    max_retries: int
    retry_delay_seconds: int
    backoff_strategy: str
    endpoints: Dict[str, str]


@dataclass
class CacheConfiguration:
    """Cache configuration data class"""
    enabled: bool
    duration_minutes: int
    key_template: str


class WorkflowConfigurationManager:
    """
    Manages workflow configuration from enhanced JSON files.
    Handles environment-specific settings and variable substitution.
    """
    
    def __init__(self, environment: Optional[str] = None):
        self.environment = environment or self._detect_environment()
        self._config_cache = {}
        logger.info(f"WorkflowConfigurationManager initialized for environment: {self.environment}")
    
    def _detect_environment(self) -> str:
        """Detect current environment from environment variables"""
        env = os.getenv('DJANGO_ENV', os.getenv('ENVIRONMENT', 'development')).lower()
        logger.debug(f"WorkflowConfigurationManager detected environment: {env}")
        return env
    
    def _substitute_variables(self, value: Any, context: Dict[str, Any] = None) -> Any:
        """
        Substitute environment variables and context variables in configuration values.
        Supports format: ${VAR_NAME:default_value}
        """
        if not isinstance(value, str):
            return value
        
        # Pattern for ${VAR_NAME:default_value} or ${VAR_NAME}
        pattern = r'\$\{([^:}]+)(?::([^}]*))?\}'
        
        def replace_var(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ''
            
            # Try environment variable first
            env_value = os.getenv(var_name)
            if env_value is not None:
                return env_value
            
            # Try context variable
            if context and var_name in context:
                return str(context[var_name])
            
            # Use default value
            return default_value
        
        return re.sub(pattern, replace_var, value)
    
    def _apply_environment_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment-specific configuration overrides"""
        base_config = config.copy()
        
        # Get environment-specific overrides
        env_config = config.get('configuration', {}).get('environments', {}).get(self.environment, {})
        
        if env_config:
            logger.debug(f"WorkflowConfigurationManager applying environment overrides for: {self.environment}")
            # Deep merge environment configuration
            configuration = base_config.get('configuration', {})
            
            # Merge API configuration
            if 'api' in env_config:
                api_config = configuration.get('api', {})
                api_config.update(env_config['api'])
                configuration['api'] = api_config
            
            # Merge cache configuration
            if 'cache' in env_config:
                cache_config = configuration.get('execution', {}).get('cache', {})
                cache_config.update(env_config['cache'])
                if 'execution' not in configuration:
                    configuration['execution'] = {}
                configuration['execution']['cache'] = cache_config
            
            # Add credentials to configuration if provided
            if 'credentials' in env_config:
                configuration['credentials'] = env_config['credentials']
            
            base_config['configuration'] = configuration
        
        return base_config
    
    def _substitute_config_variables(self, obj: Any, config: Dict[str, Any]) -> Any:
        """Recursively substitute configuration variables in the format {{config.path.to.value}}"""
        if isinstance(obj, dict):
            return {k: self._substitute_config_variables(v, config) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_config_variables(item, config) for item in obj]
        elif isinstance(obj, str):
            # Handle {{config.path.to.value}} substitution
            pattern = r'\{\{config\.([^}]+)\}\}'
            
            def replace_config_var(match):
                path = match.group(1)
                value = self._get_nested_value(config.get('configuration', {}), path)
                return str(value) if value is not None else match.group(0)
            
            return re.sub(pattern, replace_config_var, obj)
        else:
            return obj
    
    def _get_nested_value(self, obj: Dict[str, Any], path: str) -> Any:
        """Get nested value from dictionary using dot notation"""
        keys = path.split('.')
        current = obj
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def load_workflow_config(self, workflow_id: str) -> Dict[str, Any]:
        """Load and process workflow configuration"""
        if workflow_id in self._config_cache:
            logger.debug(f"WorkflowConfigurationManager returning cached config for: {workflow_id}")
            return self._config_cache[workflow_id]
        
        logger.info(f"WorkflowConfigurationManager loading config for workflow: {workflow_id}")
        
        # Load base configuration from registry
        config = self._load_raw_config(workflow_id)
        
        # Apply environment overrides
        config = self._apply_environment_overrides(config)
        
        # Substitute environment variables
        config = self._substitute_variables_recursive(config)
        
        # Substitute configuration references
        config = self._substitute_config_variables(config, config)
        
        # Cache the processed configuration
        self._config_cache[workflow_id] = config
        
        logger.info(f"WorkflowConfigurationManager successfully loaded and processed config for: {workflow_id}")
        return config
    
    def _load_raw_config(self, workflow_id: str) -> Dict[str, Any]:
        """Load raw configuration from workflow registry"""
        registry = get_workflow_registry()
        config = registry.get_workflow(workflow_id)
        
        if not config:
            logger.error(f"WorkflowConfigurationManager: Workflow configuration not found: {workflow_id}")
            raise ValueError(f"Workflow configuration not found: {workflow_id}")
        
        return config
    
    def _substitute_variables_recursive(self, obj: Any) -> Any:
        """Recursively substitute environment variables in configuration"""
        if isinstance(obj, dict):
            return {k: self._substitute_variables_recursive(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_variables_recursive(item) for item in obj]
        elif isinstance(obj, str):
            return self._substitute_variables(obj)
        else:
            return obj
    
    def get_api_config(self, workflow_config: Dict[str, Any]) -> ApiConfiguration:
        """Extract API configuration from workflow config"""
        api_config = workflow_config.get('configuration', {}).get('api', {})
        
        return ApiConfiguration(
            base_url=api_config.get('base_url', 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2'),
            timeout_seconds=api_config.get('timeout_seconds', 10),
            ssl_verify=api_config.get('ssl_verify', False),
            max_retries=api_config.get('max_retries', 3),
            retry_delay_seconds=api_config.get('retry_delay_seconds', 3),
            backoff_strategy=api_config.get('backoff_strategy', 'exponential'),
            endpoints=api_config.get('endpoints', {})
        )
    
    def get_cache_config(self, workflow_config: Dict[str, Any]) -> CacheConfiguration:
        """Extract cache configuration from workflow config"""
        cache_config = workflow_config.get('configuration', {}).get('execution', {}).get('cache', {})
        
        return CacheConfiguration(
            enabled=cache_config.get('enabled', True),
            duration_minutes=cache_config.get('duration_minutes', 60),
            key_template=cache_config.get('key_template', 'default_{workflow_id}')
        )
    
    def get_response_fields(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract response field mappings from workflow config"""
        return workflow_config.get('configuration', {}).get('response_fields', {})
    
    def get_error_messages(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract error message templates from workflow config"""
        return workflow_config.get('configuration', {}).get('error_messages', {})
    
    def get_validation_config(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract validation configuration from workflow config"""
        return workflow_config.get('configuration', {}).get('validation', {})
    
    def get_credentials(self, workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract credentials from workflow config"""
        return workflow_config.get('configuration', {}).get('credentials', {})


# Global configuration manager instance
_config_manager_instance: Optional[WorkflowConfigurationManager] = None


def get_workflow_configuration_manager() -> WorkflowConfigurationManager:
    """Get the global workflow configuration manager instance (singleton)"""
    global _config_manager_instance
    
    if _config_manager_instance is None:
        _config_manager_instance = WorkflowConfigurationManager()
    
    return _config_manager_instance


def reset_workflow_configuration_manager():
    """Reset the global configuration manager (mainly for testing)"""
    global _config_manager_instance
    _config_manager_instance = None
