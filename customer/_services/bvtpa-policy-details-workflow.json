{"schema_version": "2.1", "workflow": {"id": "bvtpa_policy_details", "name": "BVTPA Policy Details Workflow", "version": "2.1.0", "description": "Retrieves detailed policy information and claims data from BVTPA API", "category": "policy_management", "tags": ["bvtpa", "policy", "details", "claims", "insurance"]}, "configuration": {"api": {"base_url": "${TPA_BASE_URL:https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2}", "timeout_seconds": 10, "ssl_verify": false, "max_retries": 3, "retry_delay_seconds": 3, "backoff_strategy": "exponential", "endpoints": {"get_token": "/api/GetToken", "search_citizen_id": "/api/SearchCitizenID", "check_register": "/api/CheckRegister", "policy_list_social": "/api/PolicyListSocial", "policy_detail_social": "/api/PolicyDetailSocial"}}, "response_fields": {"citizen_search_results": "ListOfSearchCitizenID", "registration_check_results": "ListOfCheckRegister", "policy_list": "ListOfPolicyListSocial", "policy_details": "ListOfPolDet", "policy_claims": "ListOfPolClaim", "status_field": "Status", "member_code_field": "MemberCode"}, "validation": {"citizen_id_status_success": "1", "registration_status_success": "YES"}, "error_messages": {"max_retries_exceeded": "Maximum retries exceeded", "step_failed_template": "Step {step_id} ({step_name}) failed: {error}", "unknown_error": "Unknown error", "citizen_id_verification_failed": "Citizen ID verification failed - invalid status", "registration_verification_failed": "Registration verification failed - customer not registered", "policy_details_not_found": "No policy details found for member code"}, "data_source": {"mode": "database", "fallback_mode": "fixed", "database_queries": [{"id": "platform_identity", "table": "customer_customerplatformidentity", "fields": {"social_id": "platform_user_id", "channel_id": "channel_id", "channel": "platform"}, "where": "id = :platform_id AND is_active = true", "required": true}, {"id": "customer_data", "table": "customer_customer", "fields": {"citizen_id": "national_id"}, "where": "customer_id = :customer_id", "required": true}], "fixed_values": {"social_id": "U3ef2199803607a9ec643f2461fd2f039", "channel_id": "2006769099", "citizen_id": "2019086318637", "channel": "LINE"}}, "execution": {"timeout_minutes": 5, "retry_policy": {"default_max_retries": 3, "default_delay_seconds": 3, "backoff_strategy": "exponential"}, "cache": {"enabled": true, "duration_minutes": "${CACHE_DURATION_MINUTES:60}", "key_template": "policy_details_{customer_id}_{platform_id}_{member_code}", "environments": {"development": {"duration_minutes": 1}, "staging": {"duration_minutes": 30}, "production": {"duration_minutes": 240}}}}, "environments": {"development": {"api": {"base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2", "ssl_verify": false, "timeout_seconds": 15}, "cache": {"duration_minutes": 1}, "credentials": {"username": "BVTPA", "password": "*d!n^+Cb@1"}}, "staging": {"api": {"base_url": "https://staging.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2", "ssl_verify": true, "timeout_seconds": 10}, "cache": {"duration_minutes": 30}, "credentials": {"username": "${TPA_USERNAME}", "password": "${TPA_PASSWORD}"}}, "production": {"api": {"base_url": "https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2", "ssl_verify": true, "timeout_seconds": 8}, "cache": {"duration_minutes": 240}, "credentials": {"username": "${TPA_USERNAME}", "password": "${TPA_PASSWORD}"}}}}, "steps": [{"id": "authenticate", "name": "get_bearer_token", "type": "http_request", "description": "Authenticate with BVTPA API to obtain bearer token", "config": {"endpoint": "{{config.api.endpoints.get_token}}", "method": "POST", "headers": {"Content-Type": "application/x-www-form-urlencoded"}, "request_body": {"USERNAME": "{{config.credentials.username}}", "PASSWORD": "{{config.credentials.password}}", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "response_extraction": {"bearer_token": {"path": "$", "type": "string", "required": true, "description": "Bearer token for API authentication"}}, "retry": {"max_attempts": "{{config.execution.retry_policy.default_max_retries}}", "delay_seconds": "{{config.execution.retry_policy.default_delay_seconds}}"}}}, {"id": "fetch_policy_details", "name": "fetch_policy_details", "type": "http_request", "description": "Retrieve detailed policy information and claims data", "depends_on": ["authenticate"], "config": {"endpoint": "{{config.api.endpoints.policy_detail_social}}", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}", "Content-Type": "application/json"}, "request_body": {"CitizenID": "{{citizen_id}}", "SocialID": "{{social_id}}", "ChannelID": "{{channel_id}}", "MemberCode": "{{member_code}}"}, "response_extraction": {"policy_details": {"path": "$.{{config.response_fields.policy_details}}", "type": "array", "required": true, "description": "Detailed policy information"}, "claims_data": {"path": "$.{{config.response_fields.policy_claims}}", "type": "array", "required": false, "description": "Policy claims information"}}, "validation": {"rules": [{"id": "policy_details_exist", "type": "json_path", "path": "$.{{config.response_fields.policy_details}}", "operator": "not_empty", "error_message": "{{config.error_messages.policy_details_not_found}}", "warning_only": false}, {"id": "valid_member_code", "type": "custom_function", "function": "validate_member_code", "parameters": {"member_code": "{{member_code}}"}, "error_message": "Invalid member code format"}]}, "retry": {"max_attempts": "{{config.execution.retry_policy.default_max_retries}}", "delay_seconds": "{{config.execution.retry_policy.default_delay_seconds}}"}}}], "output": {"storage": {"enabled": true, "table": "CustomerPolicyDetails", "key_fields": ["customer_id", "member_code"], "data_mapping": {"customer_id": "{{input.customer_id}}", "member_code": "{{input.member_code}}", "citizen_id": "{{citizen_id}}", "policy_details": "{{policy_details}}", "claims_data": "{{claims_data}}", "last_updated": "{{timestamp}}"}}, "response_format": {"type": "processed", "processor": "policy_details_processor", "include_metadata": true}}, "validation": {"input_schema": {"customer_id": {"type": "integer", "required": true, "min": 1, "description": "Customer ID from database"}, "platform_id": {"type": "integer", "required": true, "min": 1, "description": "Platform identity ID"}, "member_code": {"type": "string", "required": true, "min_length": 1, "max_length": 100, "pattern": "^[A-Z0-9]+$", "description": "Policy member code"}}, "business_rules": [{"id": "customer_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customer WHERE customer_id = :customer_id", "error_message": "Customer not found in database"}, {"id": "platform_identity_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customerplatformidentity WHERE id = :platform_id AND is_active = true", "error_message": "Platform identity not found or inactive"}, {"id": "member_code_belongs_to_customer", "type": "database_check", "query": "SELECT 1 FROM CustomerPolicyList WHERE customer_id = :customer_id AND JSON_CONTAINS(member_codes, JSON_QUOTE(:member_code))", "error_message": "Member code does not belong to this customer", "warning_only": true}]}, "metadata": {"created_by": "system", "created_at": "2024-01-01T00:00:00Z", "last_modified": "2024-01-01T00:00:00Z", "documentation_url": "https://docs.example.com/workflows/bvtpa-policy-details", "support_contact": "<EMAIL>"}}