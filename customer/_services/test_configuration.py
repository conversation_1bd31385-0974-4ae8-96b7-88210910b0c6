#!/usr/bin/env python3
"""
Configuration validation script for the enhanced Policy Workflow Service.

This script validates that the configuration system is working correctly
and can load configurations for different environments.

Usage:
    python test_configuration.py [environment]

Examples:
    python test_configuration.py development
    python test_configuration.py staging
    python test_configuration.py production
"""

import os
import sys
import json
from typing import Dict, Any

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from customer._services.workflow_configuration_manager import (
        WorkflowConfigurationManager, 
        get_workflow_configuration_manager,
        reset_workflow_configuration_manager
    )
    from customer._services.policy_api_service import TPAApiService
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this script from the correct directory")
    sys.exit(1)


def test_environment_detection():
    """Test environment detection logic"""
    print("=== Testing Environment Detection ===")
    
    # Test default environment
    reset_workflow_configuration_manager()
    config_manager = WorkflowConfigurationManager()
    print(f"Default environment: {config_manager.environment}")
    
    # Test with DJANGO_ENV
    os.environ['DJANGO_ENV'] = 'staging'
    reset_workflow_configuration_manager()
    config_manager = WorkflowConfigurationManager()
    print(f"With DJANGO_ENV=staging: {config_manager.environment}")
    
    # Test with ENVIRONMENT fallback
    del os.environ['DJANGO_ENV']
    os.environ['ENVIRONMENT'] = 'production'
    reset_workflow_configuration_manager()
    config_manager = WorkflowConfigurationManager()
    print(f"With ENVIRONMENT=production: {config_manager.environment}")
    
    # Clean up
    if 'ENVIRONMENT' in os.environ:
        del os.environ['ENVIRONMENT']
    
    print()


def test_variable_substitution():
    """Test environment variable substitution"""
    print("=== Testing Variable Substitution ===")
    
    # Set test environment variables
    os.environ['TEST_VAR'] = 'test_value'
    os.environ['TPA_USERNAME'] = 'test_username'
    
    config_manager = WorkflowConfigurationManager()
    
    # Test basic substitution
    result = config_manager._substitute_variables('${TEST_VAR}')
    print(f"${TEST_VAR} -> {result}")
    
    # Test substitution with default
    result = config_manager._substitute_variables('${MISSING_VAR:default_value}')
    print(f"${{MISSING_VAR:default_value}} -> {result}")
    
    # Test no substitution needed
    result = config_manager._substitute_variables('no_substitution')
    print(f"no_substitution -> {result}")
    
    # Clean up
    del os.environ['TEST_VAR']
    del os.environ['TPA_USERNAME']
    
    print()


def test_workflow_configuration_loading():
    """Test loading workflow configurations"""
    print("=== Testing Workflow Configuration Loading ===")
    
    try:
        config_manager = get_workflow_configuration_manager()
        
        # Test policy list workflow
        print("Loading bvtpa_policy_list configuration...")
        policy_list_config = config_manager.load_workflow_config('bvtpa_policy_list')
        print(f"Schema version: {policy_list_config.get('schema_version')}")
        print(f"Workflow ID: {policy_list_config.get('workflow', {}).get('id')}")
        
        # Test policy details workflow
        print("Loading bvtpa_policy_details configuration...")
        policy_details_config = config_manager.load_workflow_config('bvtpa_policy_details')
        print(f"Schema version: {policy_details_config.get('schema_version')}")
        print(f"Workflow ID: {policy_details_config.get('workflow', {}).get('id')}")
        
        print("✓ Configuration loading successful")
        
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
    
    print()


def test_api_configuration_extraction():
    """Test API configuration extraction"""
    print("=== Testing API Configuration Extraction ===")
    
    try:
        config_manager = get_workflow_configuration_manager()
        workflow_config = config_manager.load_workflow_config('bvtpa_policy_list')
        
        # Extract API configuration
        api_config = config_manager.get_api_config(workflow_config)
        print(f"Base URL: {api_config.base_url}")
        print(f"Timeout: {api_config.timeout_seconds}s")
        print(f"SSL Verify: {api_config.ssl_verify}")
        print(f"Max Retries: {api_config.max_retries}")
        print(f"Retry Delay: {api_config.retry_delay_seconds}s")
        print(f"Endpoints: {len(api_config.endpoints)} configured")
        
        # Extract response fields
        response_fields = config_manager.get_response_fields(workflow_config)
        print(f"Response fields: {len(response_fields)} configured")
        
        # Extract cache configuration
        cache_config = config_manager.get_cache_config(workflow_config)
        print(f"Cache enabled: {cache_config.enabled}")
        print(f"Cache duration: {cache_config.duration_minutes} minutes")
        
        print("✓ Configuration extraction successful")
        
    except Exception as e:
        print(f"✗ Configuration extraction failed: {e}")
    
    print()


def test_service_initialization():
    """Test service initialization with configuration"""
    print("=== Testing Service Initialization ===")
    
    try:
        # Test TPA API Service initialization
        print("Initializing TPAApiService...")
        tpa_service = TPAApiService('bvtpa_policy_list')
        print(f"✓ TPAApiService initialized successfully")
        print(f"  Base URL: {tpa_service.api_config.base_url}")
        print(f"  Timeout: {tpa_service.api_config.timeout_seconds}s")
        
        # Test endpoint URL generation
        token_url = tpa_service.get_endpoint_url('get_token')
        print(f"  Token endpoint: {token_url}")
        
        print("✓ Service initialization successful")
        
    except Exception as e:
        print(f"✗ Service initialization failed: {e}")
    
    print()


def test_environment_specific_configuration(environment: str):
    """Test environment-specific configuration"""
    print(f"=== Testing {environment.upper()} Environment Configuration ===")
    
    # Set environment
    os.environ['DJANGO_ENV'] = environment
    reset_workflow_configuration_manager()
    
    try:
        config_manager = WorkflowConfigurationManager(environment)
        workflow_config = config_manager.load_workflow_config('bvtpa_policy_list')
        api_config = config_manager.get_api_config(workflow_config)
        cache_config = config_manager.get_cache_config(workflow_config)
        
        print(f"Environment: {config_manager.environment}")
        print(f"API Base URL: {api_config.base_url}")
        print(f"SSL Verify: {api_config.ssl_verify}")
        print(f"Timeout: {api_config.timeout_seconds}s")
        print(f"Cache Duration: {cache_config.duration_minutes} minutes")
        
        # Check credentials
        credentials = config_manager.get_credentials(workflow_config)
        if credentials:
            username = credentials.get('username', 'Not configured')
            # Don't print actual password
            password_configured = 'Yes' if credentials.get('password') else 'No'
            print(f"Username: {username}")
            print(f"Password configured: {password_configured}")
        else:
            print("Credentials: Not configured")
        
        print(f"✓ {environment.upper()} configuration loaded successfully")
        
    except Exception as e:
        print(f"✗ {environment.upper()} configuration failed: {e}")
    
    print()


def main():
    """Main test function"""
    print("Policy Workflow Service Configuration Validation")
    print("=" * 50)
    print()
    
    # Get environment from command line argument
    test_environment = sys.argv[1] if len(sys.argv) > 1 else None
    
    # Run tests
    test_environment_detection()
    test_variable_substitution()
    test_workflow_configuration_loading()
    test_api_configuration_extraction()
    test_service_initialization()
    
    # Test specific environment if provided
    if test_environment:
        if test_environment in ['development', 'staging', 'production']:
            test_environment_specific_configuration(test_environment)
        else:
            print(f"Invalid environment: {test_environment}")
            print("Valid environments: development, staging, production")
    else:
        # Test all environments
        for env in ['development', 'staging', 'production']:
            test_environment_specific_configuration(env)
    
    print("Configuration validation completed!")


if __name__ == '__main__':
    main()
