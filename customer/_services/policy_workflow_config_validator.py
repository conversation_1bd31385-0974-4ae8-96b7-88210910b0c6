import json
import re
import os
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger('django.customer_policy_crm')


class SchemaVersion(Enum):
    """Supported workflow schema versions"""
    V2_0 = "2.0"  # Enhanced format


@dataclass
class ValidationResult:
    """Result of workflow configuration validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    schema_version: str
    suggestions: List[str] = None
    
    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []


class WorkflowConfigValidator:
    """Validates workflow JSON configurations against schema versions"""
    
    CURRENT_SCHEMA_VERSION = SchemaVersion.V2_0.value
    SUPPORTED_VERSIONS = [SchemaVersion.V2_0.value]

    # Required fields for V2 schema
    V2_REQUIRED_FIELDS = {
        'root': ['schema_version', 'workflow', 'configuration', 'steps'],
        'workflow': ['id', 'name', 'version'],
        'step': ['id', 'name', 'type', 'config']
    }
    
    # Supported step types in V2
    SUPPORTED_STEP_TYPES = [
        'http_request', 'database_query', 'data_transformation', 
        'validation', 'conditional', 'loop', 'custom'
    ]
    
    # Supported HTTP methods
    SUPPORTED_HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
    
    # Supported validation operators
    SUPPORTED_OPERATORS = [
        'equals', 'not_equals', 'contains', 'not_contains',
        'greater_than', 'less_than', 'greater_equal', 'less_equal',
        'regex_match', 'in_list', 'not_in_list'
    ]
    
    @classmethod
    def validate_workflow_config(cls, config: Dict[str, Any],
                                config_file_path: str = None) -> ValidationResult:
        """
        Validate workflow configuration against V2 schema only.
        Only V2 format is supported.
        """
        logger.debug(f"WorkflowConfigValidator.validate_workflow_config: Starting V2 validation for config: {config_file_path}")

        errors = []
        warnings = []
        suggestions = []

        try:
            # Ensure this is V2 format
            schema_version = config.get('schema_version')
            if schema_version != SchemaVersion.V2_0.value:
                errors.append(f"Only V2 schema format is supported. Found schema_version: {schema_version}")
                suggestions.append("Please use V2 schema format with schema_version: '2.0'")
                return ValidationResult(False, errors, warnings, schema_version or "unknown", suggestions)

            logger.debug(f"WorkflowConfigValidator.validate_workflow_config: Validating V2 schema")

            # V2 validation
            validation_errors, validation_warnings, validation_suggestions = cls._validate_v2_config(config)
            errors.extend(validation_errors)
            warnings.extend(validation_warnings)
            suggestions.extend(validation_suggestions)

            # V2-specific validations
            common_errors, common_warnings = cls._validate_common_rules(config, SchemaVersion.V2_0.value)
            errors.extend(common_errors)
            warnings.extend(common_warnings)

            is_valid = len(errors) == 0
            logger.info(f"WorkflowConfigValidator.validate_workflow_config: V2 validation completed - valid={is_valid}, errors={len(errors)}, warnings={len(warnings)}")

            return ValidationResult(is_valid, errors, warnings, SchemaVersion.V2_0.value, suggestions)

        except Exception as e:
            logger.error(f"WorkflowConfigValidator.validate_workflow_config: Validation failed with exception: {str(e)}")
            errors.append(f"Validation error: {str(e)}")
            return ValidationResult(False, errors, warnings, "unknown", suggestions)
    
    @classmethod
    def _detect_schema_version(cls, config: Dict[str, Any]) -> str:
        """Detect schema version from configuration - V2 only"""
        # V2 has explicit schema_version field
        return config.get('schema_version', 'unknown')
    

    
    @classmethod
    def _validate_v2_config(cls, config: Dict[str, Any]) -> Tuple[List[str], List[str], List[str]]:
        """Validate V2 (enhanced) configuration format"""
        errors = []
        warnings = []
        suggestions = []
        
        # Check required root fields
        for field in cls.V2_REQUIRED_FIELDS['root']:
            if field not in config:
                errors.append(f"Missing required field: {field}")
        
        # Validate workflow metadata
        workflow_errors = cls._validate_workflow_metadata(config.get('workflow', {}))
        errors.extend(workflow_errors)
        
        # Validate configuration section
        config_errors, config_warnings = cls._validate_configuration_section(config.get('configuration', {}))
        errors.extend(config_errors)
        warnings.extend(config_warnings)
        
        # Validate steps
        steps = config.get('steps', [])
        if not isinstance(steps, list):
            errors.append("Steps must be a list")
        else:
            step_errors, step_warnings = cls._validate_v2_steps(steps)
            errors.extend(step_errors)
            warnings.extend(step_warnings)
        
        # Validate step dependencies
        dependency_errors = cls._validate_step_dependencies(steps)
        errors.extend(dependency_errors)
        
        # Validate output configuration
        if 'output' in config:
            output_errors = cls._validate_output_config(config['output'])
            errors.extend(output_errors)
        
        return errors, warnings, suggestions
    
    @classmethod
    def _validate_workflow_metadata(cls, workflow: Dict[str, Any]) -> List[str]:
        """Validate workflow metadata section"""
        errors = []
        
        # Check required fields
        for field in cls.V2_REQUIRED_FIELDS['workflow']:
            if not workflow.get(field):
                errors.append(f"Missing required workflow field: {field}")
        
        # Validate workflow ID format
        workflow_id = workflow.get('id', '')
        if workflow_id and not re.match(r'^[a-z0-9_]+$', workflow_id):
            errors.append("Workflow ID must contain only lowercase letters, numbers, and underscores")
        
        # Validate version format
        version = workflow.get('version', '')
        if version and not re.match(r'^\d+\.\d+(\.\d+)?$', version):
            errors.append("Version must follow semantic versioning format (e.g., 1.0.0)")
        
        return errors
    
    @classmethod
    def _validate_configuration_section(cls, configuration: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """Validate configuration section"""
        errors = []
        warnings = []
        
        # Validate data source
        if 'data_source' in configuration:
            data_source = configuration['data_source']
            mode = data_source.get('mode')
            
            if mode not in ['database', 'fixed', 'hybrid']:
                errors.append(f"Invalid data source mode: {mode}")
            
            if mode == 'database' and 'database_queries' not in data_source:
                errors.append("Database mode requires database_queries configuration")
            
            if mode == 'fixed' and 'fixed_values' not in data_source:
                errors.append("Fixed mode requires fixed_values configuration")
        
        # Validate execution settings
        if 'execution' in configuration:
            execution = configuration['execution']
            
            # Validate timeout
            timeout = execution.get('timeout_minutes')
            if timeout is not None and (not isinstance(timeout, (int, float)) or timeout <= 0):
                errors.append("Timeout must be a positive number")
            
            # Validate retry policy
            if 'retry_policy' in execution:
                retry_errors = cls._validate_retry_policy(execution['retry_policy'])
                errors.extend(retry_errors)
        
        return errors, warnings

    @classmethod
    def _validate_retry_policy(cls, retry_policy: Dict[str, Any]) -> List[str]:
        """Validate retry policy configuration"""
        errors = []

        max_retries = retry_policy.get('default_max_retries')
        if max_retries is not None and (not isinstance(max_retries, int) or max_retries < 0):
            errors.append("default_max_retries must be a non-negative integer")

        delay = retry_policy.get('default_delay_seconds')
        if delay is not None and (not isinstance(delay, (int, float)) or delay < 0):
            errors.append("default_delay_seconds must be a non-negative number")

        backoff = retry_policy.get('backoff_strategy', 'linear')
        if backoff not in ['linear', 'exponential', 'fixed']:
            errors.append(f"Invalid backoff strategy: {backoff}")

        return errors



    @classmethod
    def _validate_v2_steps(cls, steps: List[Dict[str, Any]]) -> Tuple[List[str], List[str]]:
        """Validate V2 format steps"""
        errors = []
        warnings = []
        step_ids = set()

        for i, step in enumerate(steps):
            step_prefix = f"Step {i + 1}"

            # Check required fields
            for field in cls.V2_REQUIRED_FIELDS['step']:
                if field not in step:
                    errors.append(f"{step_prefix}: Missing required field '{field}'")

            # Check for duplicate step IDs
            step_id = step.get('id')
            if step_id in step_ids:
                errors.append(f"{step_prefix}: Duplicate step ID '{step_id}'")
            step_ids.add(step_id)

            # Validate step type
            step_type = step.get('type')
            if step_type not in cls.SUPPORTED_STEP_TYPES:
                errors.append(f"{step_prefix}: Invalid step type '{step_type}'")

            # Validate step configuration based on type
            config = step.get('config', {})
            if step_type == 'http_request':
                http_errors = cls._validate_http_request_config(config, step_prefix)
                errors.extend(http_errors)
            elif step_type == 'validation':
                validation_errors = cls._validate_validation_config(config, step_prefix)
                errors.extend(validation_errors)

        return errors, warnings

    @classmethod
    def _validate_http_request_config(cls, config: Dict[str, Any], step_prefix: str) -> List[str]:
        """Validate HTTP request step configuration"""
        errors = []

        # Required fields for HTTP request
        required_fields = ['endpoint', 'method']
        for field in required_fields:
            if field not in config:
                errors.append(f"{step_prefix}: Missing required HTTP config field '{field}'")

        # Validate HTTP method
        method = config.get('method', '').upper()
        if method and method not in cls.SUPPORTED_HTTP_METHODS:
            errors.append(f"{step_prefix}: Invalid HTTP method '{method}'")

        # Validate headers
        headers = config.get('headers', {})
        if not isinstance(headers, dict):
            errors.append(f"{step_prefix}: Headers must be a dictionary")

        # Validate response extraction
        if 'response_extraction' in config:
            extraction = config['response_extraction']
            if not isinstance(extraction, dict):
                errors.append(f"{step_prefix}: response_extraction must be a dictionary")
            else:
                for key, extraction_config in extraction.items():
                    if not isinstance(extraction_config, dict):
                        errors.append(f"{step_prefix}: Invalid extraction config for '{key}'")
                    elif 'path' not in extraction_config:
                        errors.append(f"{step_prefix}: Missing 'path' in extraction config for '{key}'")

        return errors

    @classmethod
    def _validate_validation_config(cls, config: Dict[str, Any], step_prefix: str) -> List[str]:
        """Validate validation step configuration"""
        errors = []

        rules = config.get('rules', [])
        if not isinstance(rules, list):
            errors.append(f"{step_prefix}: Validation rules must be a list")
            return errors

        for i, rule in enumerate(rules):
            rule_prefix = f"{step_prefix}, Rule {i + 1}"

            if not isinstance(rule, dict):
                errors.append(f"{rule_prefix}: Rule must be a dictionary")
                continue

            # Check required rule fields
            if 'type' not in rule:
                errors.append(f"{rule_prefix}: Missing required field 'type'")

            rule_type = rule.get('type')
            if rule_type == 'json_path':
                if 'path' not in rule:
                    errors.append(f"{rule_prefix}: json_path rule requires 'path' field")
                if 'operator' not in rule:
                    errors.append(f"{rule_prefix}: json_path rule requires 'operator' field")

                operator = rule.get('operator')
                if operator and operator not in cls.SUPPORTED_OPERATORS:
                    errors.append(f"{rule_prefix}: Invalid operator '{operator}'")

        return errors

    @classmethod
    def _validate_step_dependencies(cls, steps: List[Dict[str, Any]]) -> List[str]:
        """Validate step dependencies"""
        errors = []
        step_ids = {step.get('id') for step in steps if step.get('id')}

        for step in steps:
            depends_on = step.get('depends_on', [])
            if not isinstance(depends_on, list):
                errors.append(f"Step '{step.get('id')}': depends_on must be a list")
                continue

            for dependency in depends_on:
                if dependency not in step_ids:
                    errors.append(f"Step '{step.get('id')}': Unknown dependency '{dependency}'")

        return errors

    @classmethod
    def _validate_output_config(cls, output: Dict[str, Any]) -> List[str]:
        """Validate output configuration"""
        errors = []

        if 'storage' in output:
            storage = output['storage']
            if storage.get('enabled') and 'table' not in storage:
                errors.append("Storage configuration requires 'table' field when enabled")

        return errors

    @classmethod
    def _validate_common_rules(cls, config: Dict[str, Any], schema_version: str) -> Tuple[List[str], List[str]]:
        """Validate common rules across all schema versions"""
        errors = []
        warnings = []

        # Check for circular dependencies in steps
        steps = config.get('steps', [])
        if cls._has_circular_dependencies(steps):
            errors.append("Circular dependencies detected in workflow steps")

        # Validate template variable usage
        template_vars = cls._extract_all_template_variables(config)
        if template_vars:
            warnings.append(f"Template variables found: {', '.join(template_vars)}")

        return errors, warnings

    @classmethod
    def _has_circular_dependencies(cls, steps: List[Dict[str, Any]]) -> bool:
        """Check for circular dependencies in step definitions"""
        # Build dependency graph
        dependencies = {}
        for step in steps:
            step_id = step.get('id')
            depends_on = step.get('depends_on', [])
            dependencies[step_id] = depends_on

        # Check for cycles using DFS
        visited = set()
        rec_stack = set()

        def has_cycle(node):
            if node in rec_stack:
                return True
            if node in visited:
                return False

            visited.add(node)
            rec_stack.add(node)

            for neighbor in dependencies.get(node, []):
                if has_cycle(neighbor):
                    return True

            rec_stack.remove(node)
            return False

        for step_id in dependencies:
            if has_cycle(step_id):
                return True

        return False

    @classmethod
    def _extract_template_variables(cls, text: str) -> List[str]:
        """Extract template variables from text"""
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, text)
        return [match.strip() for match in matches]

    @classmethod
    def _extract_all_template_variables(cls, config: Dict[str, Any]) -> List[str]:
        """Extract all template variables from configuration"""
        config_str = json.dumps(config)
        return list(set(cls._extract_template_variables(config_str)))
