# Policy Workflow Service Environment Configuration Example
# Copy this file to .env and configure for your environment

# =============================================================================
# ENVIRONMENT DETECTION
# =============================================================================

# Primary environment variable (required)
# Supported values: development, staging, production
DJANGO_ENV=development

# Alternative environment variable (fallback)
# ENVIRONMENT=development

# =============================================================================
# TPA API CONFIGURATION
# =============================================================================

# TPA API Credentials (required for staging/production)
# Development uses hardcoded credentials for testing
TPA_USERNAME=your_tpa_username_here
TPA_PASSWORD=your_tpa_password_here

# TPA API Base URL (optional - uses environment-specific defaults)
# Development: https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2
# Staging: https://staging.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2  
# Production: https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2
# TPA_BASE_URL=https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Cache duration in minutes (optional - uses environment-specific defaults)
# Development: 1 minute
# Staging: 30 minutes
# Production: 240 minutes (4 hours)
# CACHE_DURATION_MINUTES=60

# =============================================================================
# ENVIRONMENT-SPECIFIC EXAMPLES
# =============================================================================

# --- DEVELOPMENT ENVIRONMENT ---
# DJANGO_ENV=development
# # No additional configuration needed - uses hardcoded development settings

# --- STAGING ENVIRONMENT ---
# DJANGO_ENV=staging
# TPA_USERNAME=staging_username
# TPA_PASSWORD=staging_password
# CACHE_DURATION_MINUTES=30

# --- PRODUCTION ENVIRONMENT ---
# DJANGO_ENV=production
# TPA_USERNAME=production_username
# TPA_PASSWORD=production_password
# TPA_BASE_URL=https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2
# CACHE_DURATION_MINUTES=240

# =============================================================================
# ADDITIONAL DJANGO SETTINGS (if needed)
# =============================================================================

# Django debug mode
# DEBUG=False

# Django secret key
# SECRET_KEY=your_secret_key_here

# Database configuration
# DATABASE_URL=postgresql://user:password@localhost:5432/dbname
