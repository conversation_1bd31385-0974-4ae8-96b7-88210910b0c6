# Policy Workflow Service Deployment Guide

## Overview

This guide covers the deployment of the enhanced, configuration-driven Policy Workflow Service that was refactored from hardcoded constants to JSON-based configuration.

## Environment Configuration

### Environment Detection

The system automatically detects the environment using the following environment variables (in order of precedence):

1. `DJANGO_ENV` - Primary environment variable
2. `ENVIRONMENT` - Fallback environment variable
3. Default: `development`

Supported environments:
- `development`
- `staging` 
- `production`

### Environment Variables

The following environment variables can be configured for each environment:

#### Required Variables

```bash
# TPA API Credentials (staging/production)
TPA_USERNAME=your_tpa_username
TPA_PASSWORD=your_tpa_password

# TPA API Base URL (optional, defaults to environment-specific URLs)
TPA_BASE_URL=https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2

# Cache Duration (optional, defaults to environment-specific values)
CACHE_DURATION_MINUTES=240
```

#### Optional Variables

```bash
# Django Environment
DJANGO_ENV=production

# Alternative environment variable
ENVIRONMENT=production
```

### Environment-Specific Configuration

#### Development Environment

- **API Base URL**: `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2`
- **SSL Verification**: Disabled
- **Timeout**: 15 seconds
- **Cache Duration**: 1 minute
- **Credentials**: Hardcoded (for development only)

#### Staging Environment

- **API Base URL**: `https://staging.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2`
- **SSL Verification**: Enabled
- **Timeout**: 10 seconds
- **Cache Duration**: 30 minutes
- **Credentials**: From environment variables

#### Production Environment

- **API Base URL**: `https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2`
- **SSL Verification**: Enabled
- **Timeout**: 8 seconds
- **Cache Duration**: 240 minutes (4 hours)
- **Credentials**: From environment variables

## Deployment Steps

### 1. Environment Variables Setup

Create environment-specific configuration files:

**Development (.env.development)**
```bash
DJANGO_ENV=development
# No additional variables needed - uses hardcoded development credentials
```

**Staging (.env.staging)**
```bash
DJANGO_ENV=staging
TPA_USERNAME=staging_username
TPA_PASSWORD=staging_password
CACHE_DURATION_MINUTES=30
```

**Production (.env.production)**
```bash
DJANGO_ENV=production
TPA_USERNAME=production_username
TPA_PASSWORD=production_password
TPA_BASE_URL=https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2
CACHE_DURATION_MINUTES=240
```

### 2. Configuration Validation

The system includes built-in configuration validation. To test configuration loading:

```python
from customer._services.workflow_configuration_manager import get_workflow_configuration_manager

# Test configuration loading
config_manager = get_workflow_configuration_manager()
policy_list_config = config_manager.load_workflow_config('bvtpa_policy_list')
policy_details_config = config_manager.load_workflow_config('bvtpa_policy_details')

print(f"Environment: {config_manager.environment}")
print(f"API Base URL: {config_manager.get_api_config(policy_list_config).base_url}")
```

### 3. Service Initialization

The services now automatically use the configuration manager:

```python
from customer._services.policy_api_service import TPAApiService
from customer._services.policy_workflow_service import PolicyWorkflowService

# Services automatically detect environment and load appropriate configuration
tpa_service = TPAApiService('bvtpa_policy_list')
workflow_service = PolicyWorkflowService()
```

### 4. Monitoring and Logging

The enhanced services provide detailed logging for configuration loading:

- Configuration manager initialization
- Environment detection
- Configuration loading and processing
- Environment variable substitution
- API configuration details (excluding sensitive data)

Monitor logs for:
- `WorkflowConfigurationManager initialized for environment: {env}`
- `WorkflowConfigurationManager loading config for workflow: {workflow_id}`
- `TPAApiService.__init__: Configuration loaded - base_url={url}, timeout={timeout}s`

## Migration from Legacy System

### Backward Compatibility

The refactored system maintains backward compatibility:

1. **Existing API**: All public methods remain unchanged
2. **Response Format**: Response data structure is preserved
3. **Error Handling**: Error types and messages are consistent
4. **Database Schema**: No database changes required

### Migration Checklist

- [ ] Deploy updated JSON configuration files
- [ ] Set environment variables for staging/production
- [ ] Update deployment scripts to include environment detection
- [ ] Test configuration loading in each environment
- [ ] Verify API connectivity with new configuration
- [ ] Monitor logs for configuration-related issues
- [ ] Validate response data format consistency

## Troubleshooting

### Common Issues

1. **Configuration Not Found**
   - Error: `Workflow configuration not found: {workflow_id}`
   - Solution: Ensure JSON configuration files are deployed

2. **Environment Variable Missing**
   - Error: Credentials not found or API calls failing
   - Solution: Set required environment variables (TPA_USERNAME, TPA_PASSWORD)

3. **SSL Certificate Issues**
   - Error: SSL verification failed
   - Solution: Check environment-specific SSL settings in configuration

4. **Cache Issues**
   - Error: Unexpected cache behavior
   - Solution: Verify CACHE_DURATION_MINUTES environment variable

### Debug Mode

Enable debug logging for detailed configuration information:

```python
import logging
logging.getLogger('django.customer_policy_crm').setLevel(logging.DEBUG)
```

## Performance Considerations

### Configuration Caching

- Configuration is cached per workflow to avoid repeated file I/O
- Cache is invalidated when configuration manager is reset
- Environment-specific settings are applied once during initialization

### API Performance

- Environment-specific timeout settings optimize for network conditions
- Retry policies use exponential backoff for better resilience
- SSL verification settings balance security and performance

## Security Considerations

### Credential Management

- Development: Hardcoded credentials (acceptable for development only)
- Staging/Production: Environment variables only
- No credentials stored in configuration files
- Sensitive data excluded from logs

### SSL Configuration

- Development: SSL verification disabled (for testing with self-signed certificates)
- Staging/Production: SSL verification enabled
- Environment-specific certificate validation

## Rollback Plan

If issues occur, rollback steps:

1. Revert to previous service versions
2. Restore original hardcoded configuration
3. Remove environment variables
4. Monitor for stability

The system is designed for safe rollback with minimal impact.
