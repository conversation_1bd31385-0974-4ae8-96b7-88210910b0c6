# Policy Workflow Service Refactoring Recommendations

## Executive Summary

This document provides specific recommendations for refactoring hardcoded constants from Python files into JSON workflow configuration files to create a more maintainable, configuration-driven system.

## Current State Analysis

### Constants in `policy_workflow_service.py`
- **Retry Settings**: `DEFAULT_RETRY_DELAY_SECONDS = 3`, `DEFAULT_MAX_RETRIES = 1`
- **Cache Settings**: `DEFAULT_CACHE_DURATION_MINUTES = 1`
- **Field Names**: API response field constants (e.g., `FIELD_LIST_OF_SEARCH_CITIZEN_ID`)
- **Context Keys**: Internal processing keys
- **Error Messages**: Hardcoded error message templates
- **Workflow Mappings**: Legacy workflow type to ID mappings

### Constants in `policy_api_service.py`
- **API Configuration**: `DEFAULT_TIMEOUT = 10`, `DEFAULT_BASE_URL`, `DEFAULT_SSL_VERIFY = 'false'`
- **Retry Settings**: `MAX_RETRIES = 1`, `RETRY_DELAY = 3`
- **API Endpoints**: All endpoint paths
- **Content Types**: HTTP content type constants

### JSON Configuration Files
- Already contain some configuration (retry policies, cache settings)
- Have hardcoded credentials in authentication steps
- Contain endpoint paths and validation rules

## Refactoring Recommendations

### 1. High Priority - Move to JSON Configuration

#### A. API Configuration Settings
**Current Python Constants → JSON Location**
```python
# FROM policy_api_service.py
DEFAULT_TIMEOUT = 10
DEFAULT_BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"
DEFAULT_SSL_VERIFY = 'false'
```

**Move to JSON configuration section:**
```json
{
  "configuration": {
    "api": {
      "base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2",
      "timeout_seconds": 10,
      "ssl_verify": false,
      "max_retries": 3,
      "retry_delay_seconds": 3,
      "backoff_strategy": "exponential"
    }
  }
}
```

#### B. Cache Configuration Consolidation
**Current Python Constants → JSON Enhancement**
```python
# FROM policy_workflow_service.py
DEFAULT_CACHE_DURATION_MINUTES = 1
```

**Enhance existing JSON cache configuration:**
```json
{
  "configuration": {
    "cache": {
      "enabled": true,
      "duration_minutes": 60,  // Move from Python constant
      "key_template": "policy_list_{customer_id}_{platform_id}",
      "environments": {
        "development": {"duration_minutes": 1},
        "production": {"duration_minutes": 240}
      }
    }
  }
}
```

#### C. Response Field Mappings
**Current Python Constants → JSON Configuration**
```python
# FROM policy_workflow_service.py
FIELD_LIST_OF_SEARCH_CITIZEN_ID = 'ListOfSearchCitizenID'
FIELD_LIST_OF_CHECK_REGISTER = 'ListOfCheckRegister'
# ... etc
```

**Add to JSON configuration:**
```json
{
  "configuration": {
    "response_fields": {
      "citizen_search_results": "ListOfSearchCitizenID",
      "registration_check_results": "ListOfCheckRegister",
      "policy_list": "ListOfPolicyListSocial",
      "policy_details": "ListOfPolDet",
      "policy_claims": "ListOfPolClaim",
      "status_field": "Status",
      "member_code_field": "MemberCode"
    }
  }
}
```

### 2. Medium Priority - Consolidate Shared Constants

#### A. Create Common Configuration Section
**Add shared configuration that both workflows can inherit:**
```json
{
  "configuration": {
    "common": {
      "retry_policy": {
        "default_max_retries": 3,
        "default_delay_seconds": 3,
        "backoff_strategy": "exponential"
      },
      "validation": {
        "citizen_id_status_success": "1",
        "registration_status_success": "YES"
      },
      "error_messages": {
        "max_retries_exceeded": "Maximum retries exceeded",
        "step_failed_template": "Step {step_id} ({step_name}) failed: {error}",
        "unknown_error": "Unknown error"
      }
    }
  }
}
```

#### B. Environment-Specific Configuration
**Add environment-aware configuration:**
```json
{
  "configuration": {
    "environments": {
      "development": {
        "api": {
          "base_url": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2",
          "ssl_verify": false
        },
        "cache": {"duration_minutes": 1},
        "credentials": {
          "username": "BVTPA",
          "password": "*d!n^+Cb@1"
        }
      },
      "production": {
        "api": {
          "base_url": "https://api.thirdpartyadmin.co.th:443/TPA.TMS.Web.API_PREPROV2",
          "ssl_verify": true
        },
        "cache": {"duration_minutes": 240},
        "credentials": {
          "username": "${TPA_USERNAME}",
          "password": "${TPA_PASSWORD}"
        }
      }
    }
  }
}
```

### 3. Low Priority - Keep in Python

#### A. Internal Processing Constants
**Keep these in Python (not configuration-dependent):**
```python
# Context keys for internal processing
CONTEXT_STEP_DATA = 'step_data'
CONTEXT_STEP_RESULTS = 'step_results'
CONTEXT_TPA_CALLS = 'tpa_calls'
# ... etc

# Result keys for internal processing
RESULT_SUCCESS = 'success'
RESULT_ERROR = 'error'
# ... etc

# Template pattern for variable resolution
TEMPLATE_VARIABLE_PATTERN = r'\{\{([^}]+)\}\}'
```

**Rationale**: These are internal implementation details that don't need external configuration.

### 4. Constants to Eliminate Entirely

#### A. Duplicate Retry Settings
**Current Duplication:**
- `policy_workflow_service.py`: `DEFAULT_RETRY_DELAY_SECONDS = 3`, `DEFAULT_MAX_RETRIES = 1`
- `policy_api_service.py`: `RETRY_DELAY = 3`, `MAX_RETRIES = 1`
- JSON files: Individual step retry configurations

**Solution**: Use single source of truth in JSON configuration with inheritance.

#### B. Hardcoded Workflow Mappings
**Current:**
```python
workflow_id_mapping = {
    'POLICY_LIST': 'bvtpa_policy_list',
    'POLICY_DETAILS': 'bvtpa_policy_details'
}
```

**Solution**: Move to JSON configuration or eliminate by using workflow IDs directly.

## Implementation Plan

### Phase 1: JSON Configuration Enhancement
1. Add `api`, `response_fields`, and `common` sections to both JSON files
2. Create environment-specific configuration sections
3. Consolidate retry and cache settings

### Phase 2: Python Code Updates
1. Update `TPAApiService` to read configuration from JSON
2. Update `GenericWorkflowExecutor` to use JSON-based field mappings
3. Add configuration loader utility methods

### Phase 3: Environment Configuration
1. Implement environment detection logic
2. Add support for environment variable substitution
3. Update deployment configurations

## Code Changes Required

### A. Enhanced JSON Configuration Structure
Both workflow JSON files should be updated with the enhanced configuration structure shown above.

### B. Python Configuration Reader
```python
class WorkflowConfigurationManager:
    """Manages workflow configuration from JSON files"""
    
    @staticmethod
    def get_api_config(workflow_config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract API configuration from workflow config"""
        return workflow_config.get('configuration', {}).get('api', {})
    
    @staticmethod
    def get_response_fields(workflow_config: Dict[str, Any]) -> Dict[str, str]:
        """Extract response field mappings from workflow config"""
        return workflow_config.get('configuration', {}).get('response_fields', {})
```

### C. Updated Service Classes
- `TPAApiService.__init__()`: Read API configuration from JSON
- `GenericWorkflowExecutor`: Use JSON-based field mappings
- Add environment detection and configuration selection logic

## Risk Assessment and Mitigation

### Risks
1. **Breaking Changes**: Modifying configuration structure could break existing workflows
2. **Environment Complexity**: Environment-specific configuration adds complexity
3. **Credential Security**: Moving credentials to JSON files requires secure handling

### Mitigation Strategies
1. **Backward Compatibility**: Maintain fallback to Python constants during transition
2. **Gradual Migration**: Implement changes incrementally with feature flags
3. **Secure Configuration**: Use environment variables for sensitive data
4. **Comprehensive Testing**: Test all configuration combinations thoroughly

## Benefits

### Maintainability
- Single source of truth for configuration
- Environment-specific settings without code changes
- Easier configuration management for operations teams

### Flexibility
- Runtime configuration changes without code deployment
- A/B testing of different API settings
- Easy addition of new environments

### Security
- Credentials externalized from code
- Environment-specific security settings
- Audit trail for configuration changes

## Next Steps

1. **Review and Approve**: Stakeholder review of recommendations
2. **Create Implementation Tasks**: Break down into specific development tasks
3. **Set Up Testing Environment**: Prepare for configuration testing
4. **Begin Phase 1**: Start with JSON configuration enhancement
5. **Monitor and Iterate**: Track benefits and adjust approach as needed
